{% extends "base.html" %}

{% block title %}Console - Minecraft Server Manager{% endblock %}

{% block content %}
<div class="row g-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-terminal me-2"></i>Server Console
                    <span class="badge bg-success ms-2 animate__animated animate__pulse animate__infinite" style="font-size: 0.7rem;">LIVE</span>
                </h5>
                <div class="d-flex gap-2">
                    <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()" data-bs-toggle="tooltip" title="Refresh logs">
                        <i class="fas fa-sync-alt"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="toggleAutoRefresh()" data-bs-toggle="tooltip" title="Toggle auto-refresh">
                        <i class="fas fa-play" id="autoRefreshIcon"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="clearConsole()" data-bs-toggle="tooltip" title="Clear console">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="downloadLogs()" data-bs-toggle="tooltip" title="Download logs">
                        <i class="fas fa-download"></i>
                    </button>
                </div>
            </div>
            <div class="card-body p-0 position-relative">
                <div class="console-output" id="consoleOutput">
                    <div class="console-content pt-4">
                        {{ logs if logs else "Loading console logs..." }}
                    </div>
                </div>
                <div class="position-absolute top-0 end-0 p-2">
                    <small class="text-muted">
                        <i class="fas fa-circle text-success me-1" style="font-size: 0.5rem;"></i>
                        Connected
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-12">
        <div class="card glass-intense animate__animated animate__fadeInUp animate__delay-1s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-keyboard me-2"></i>Command Executor
                </h5>
            </div>
            <div class="card-body">
                <div class="alert alert-warning border-0 bg-warning bg-opacity-10 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle text-warning fs-5 me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Command Execution Warning</h6>
                            <p class="mb-0">Be careful when executing commands. Some commands can affect server performance or player experience.</p>
                        </div>
                    </div>
                </div>

                <form id="commandForm" class="mb-4">
                    <div class="input-group input-group-lg">
                        <span class="input-group-text bg-dark text-light">
                            <i class="fas fa-terminal"></i>
                        </span>
                        <span class="input-group-text bg-dark text-light border-start-0">/</span>
                        <input type="text" class="form-control" id="commandInput" placeholder="Enter command (without /)" required autocomplete="off">
                        <button class="btn btn-primary" type="submit">
                            <i class="fas fa-paper-plane me-2"></i>Execute
                        </button>
                    </div>
                    <div class="form-text mt-2">
                        <i class="fas fa-lightbulb me-1"></i>
                        Press <kbd>Tab</kbd> for command suggestions, <kbd>↑</kbd> for command history
                    </div>
                </form>

                <div class="row">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-star me-2 text-warning"></i>Common Commands
                        </h6>
                        <div class="d-flex flex-wrap gap-2">
                            <button class="btn btn-sm btn-outline-secondary" onclick="insertCommand('list')">
                                <code>list</code>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="insertCommand('say Hello everyone!')">
                                <code>say</code>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="insertCommand('time set day')">
                                <code>time set</code>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="insertCommand('weather clear')">
                                <code>weather</code>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="insertCommand('gamemode creative')">
                                <code>gamemode</code>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-3">
                            <i class="fas fa-history me-2 text-info"></i>Command History
                        </h6>
                        <div id="commandHistory" class="glass rounded p-2" style="height: 100px; overflow-y: auto; background-color: rgba(17, 25, 40, 0.6);">
                            <small class="text-muted">Command history will appear here...</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4 g-4">
    <div class="col-md-6">
        <div class="card glass-intense animate__animated animate__fadeInLeft animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>Quick Commands
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    <div class="col-6">
                        <button class="btn btn-outline-primary w-100" onclick="executeQuickCommand('list')">
                            <i class="fas fa-users d-block mb-1"></i>
                            <small>List Players</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-warning w-100" onclick="executeQuickCommand('time set day')">
                            <i class="fas fa-sun d-block mb-1"></i>
                            <small>Set Day</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-secondary w-100" onclick="executeQuickCommand('time set night')">
                            <i class="fas fa-moon d-block mb-1"></i>
                            <small>Set Night</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-success w-100" onclick="executeQuickCommand('weather clear')">
                            <i class="fas fa-cloud-sun d-block mb-1"></i>
                            <small>Clear Weather</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-info w-100" onclick="executeQuickCommand('weather rain')">
                            <i class="fas fa-cloud-rain d-block mb-1"></i>
                            <small>Make Rain</small>
                        </button>
                    </div>
                    <div class="col-6">
                        <button class="btn btn-outline-danger w-100" onclick="executeQuickCommand('stop')">
                            <i class="fas fa-stop d-block mb-1"></i>
                            <small>Stop Server</small>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card glass-intense animate__animated animate__fadeInRight animate__delay-2s">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>Console Status
                </h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-primary bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-sync-alt text-primary"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <small class="text-muted d-block">Auto-refresh</small>
                                <span class="fw-medium" id="autoRefreshStatus">Disabled</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-success bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-clock text-success"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <small class="text-muted d-block">Last updated</small>
                                <span class="fw-medium" id="lastUpdated">Now</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-info bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-list text-info"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <small class="text-muted d-block">Log lines</small>
                                <span class="fw-medium">Last 100</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-10 rounded-circle p-2">
                                    <i class="fas fa-database text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-2">
                                <small class="text-muted d-block">Buffer size</small>
                                <span class="fw-medium">5MB</span>
                            </div>
                        </div>
                    </div>
                </div>
                <hr>
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Real-time server logs with player activity, events, and command outputs.
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let autoRefreshEnabled = false;
let autoRefreshInterval;
let commandHistory = [];
let historyIndex = -1;

function refreshLogs() {
    const btn = event?.target?.closest('button');
    const icon = btn?.querySelector('i');

    if (icon) {
        icon.classList.add('fa-spin');
    }

    fetch('/api/console?lines=100')
        .then(response => response.json())
        .then(data => {
            if (data.logs) {
                const consoleContent = document.querySelector('.console-content');
                consoleContent.textContent = data.logs;

                // Scroll to bottom with smooth animation
                const consoleOutput = document.getElementById('consoleOutput');
                consoleOutput.scrollTo({
                    top: consoleOutput.scrollHeight,
                    behavior: 'smooth'
                });

                // Update last updated time
                document.getElementById('lastUpdated').textContent = new Date().toLocaleTimeString();

                ToastManager.show('Console logs refreshed!', 'success', 2000);
            }
        })
        .catch(error => {
            console.error('Error refreshing logs:', error);
            ToastManager.show('Failed to refresh logs', 'error');
        })
        .finally(() => {
            if (icon) {
                icon.classList.remove('fa-spin');
            }
        });
}

function toggleAutoRefresh() {
    autoRefreshEnabled = !autoRefreshEnabled;
    const icon = document.getElementById('autoRefreshIcon');
    const status = document.getElementById('autoRefreshStatus');

    if (autoRefreshEnabled) {
        icon.className = 'fas fa-pause';
        status.textContent = 'Enabled';
        status.classList.add('text-success');
        status.classList.remove('text-muted');
        autoRefreshInterval = setInterval(refreshLogs, 5000);
        ToastManager.show('Auto-refresh enabled (every 5 seconds)', 'info');
    } else {
        icon.className = 'fas fa-play';
        status.textContent = 'Disabled';
        status.classList.add('text-muted');
        status.classList.remove('text-success');
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
        ToastManager.show('Auto-refresh disabled', 'info');
    }
}

function clearConsole() {
    Swal.fire({
        title: 'Clear Console?',
        text: 'This will clear the current console view (logs will still be available on refresh)',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#6366f1',
        cancelButtonColor: '#6b7280',
        confirmButtonText: 'Yes, clear it!'
    }).then((result) => {
        if (result.isConfirmed) {
            const consoleContent = document.querySelector('.console-content');
            consoleContent.textContent = 'Console cleared...\n';
            ToastManager.show('Console cleared!', 'success');
        }
    });
}

function downloadLogs() {
    fetch('/api/console?lines=1000')
        .then(response => response.json())
        .then(data => {
            if (data.logs) {
                const blob = new Blob([data.logs], { type: 'text/plain' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `minecraft-server-logs-${new Date().toISOString().split('T')[0]}.txt`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                ToastManager.show('Logs downloaded successfully!', 'success');
            }
        })
        .catch(error => {
            ToastManager.show('Failed to download logs', 'error');
        });
}

function insertCommand(command) {
    const input = document.getElementById('commandInput');
    input.value = command;
    input.focus();

    // Add visual feedback
    input.classList.add('animate__animated', 'animate__pulse');
    setTimeout(() => {
        input.classList.remove('animate__animated', 'animate__pulse');
    }, 1000);
}

function executeQuickCommand(command) {
    const input = document.getElementById('commandInput');
    input.value = command;

    // Add visual feedback to the button
    const btn = event.target.closest('button');
    btn.classList.add('animate__animated', 'animate__pulse');
    setTimeout(() => {
        btn.classList.remove('animate__animated', 'animate__pulse');
    }, 1000);

    // Execute the command
    document.getElementById('commandForm').dispatchEvent(new Event('submit'));
}

function addToHistory(command) {
    if (command && !commandHistory.includes(command)) {
        commandHistory.unshift(command);
        if (commandHistory.length > 10) {
            commandHistory.pop();
        }
        updateHistoryDisplay();
    }
    historyIndex = -1;
}

function updateHistoryDisplay() {
    const historyDiv = document.getElementById('commandHistory');
    if (commandHistory.length === 0) {
        historyDiv.innerHTML = '<small class="text-muted">Command history will appear here...</small>';
    } else {
        historyDiv.innerHTML = commandHistory.map(cmd =>
            `<div class="mb-1">
                <button class="btn btn-sm btn-outline-secondary w-100 text-start" onclick="insertCommand('${cmd}')">
                    <code>/${cmd}</code>
                </button>
            </div>`
        ).join('');
    }
}

// Enhanced command form handling
document.addEventListener('DOMContentLoaded', function() {
    const commandForm = document.getElementById('commandForm');
    const commandInput = document.getElementById('commandInput');

    // Handle form submission
    commandForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const command = commandInput.value.trim();

        if (!command) return;

        // Add to history
        addToHistory(command);

        // Visual feedback
        const submitBtn = this.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Executing...';
        submitBtn.disabled = true;

        // Show command in console
        const consoleContent = document.querySelector('.console-content');
        const timestamp = new Date().toLocaleTimeString();
        consoleContent.textContent += `\n[${timestamp}] > /${command}\n[Command executed - check server logs for output]\n`;

        // Scroll to bottom
        const consoleOutput = document.getElementById('consoleOutput');
        consoleOutput.scrollTo({
            top: consoleOutput.scrollHeight,
            behavior: 'smooth'
        });

        // Clear input and restore button
        setTimeout(() => {
            commandInput.value = '';
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
            ToastManager.show(`Command "/${command}" executed!`, 'success');

            // Refresh logs to see output
            setTimeout(refreshLogs, 1000);
        }, 1000);
    });

    // Handle keyboard shortcuts
    commandInput.addEventListener('keydown', function(e) {
        if (e.key === 'ArrowUp') {
            e.preventDefault();
            if (historyIndex < commandHistory.length - 1) {
                historyIndex++;
                this.value = commandHistory[historyIndex];
            }
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            if (historyIndex > 0) {
                historyIndex--;
                this.value = commandHistory[historyIndex];
            } else if (historyIndex === 0) {
                historyIndex = -1;
                this.value = '';
            }
        }
    });

    // Auto-scroll console to bottom (only if console exists)
    const consoleOutput = document.getElementById('consoleOutput');
    if (consoleOutput) {
        consoleOutput.scrollTop = consoleOutput.scrollHeight;
    }

    // Initialize history display
    updateHistoryDisplay();
});
</script>
{% endblock %}
