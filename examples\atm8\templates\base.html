<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="format-detection" content="telephone=no">
    <title>{% block title %}Minecraft Server Manager{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
    <style>
        :root {
            /* Minecraft Green Theme Colors */
            --primary-color: #4ade80;
            --primary-light: #86efac;
            --primary-dark: #22c55e;
            --secondary-color: #10b981;
            --secondary-light: #34d399;
            --secondary-dark: #059669;
            --success-color: #22c55e;
            --success-light: #4ade80;
            --success-dark: #16a34a;
            --warning-color: #eab308;
            --warning-light: #facc15;
            --warning-dark: #ca8a04;
            --danger-color: #ef4444;
            --danger-light: #f87171;
            --danger-dark: #dc2626;
            --info-color: #06b6d4;
            --info-light: #22d3ee;
            --info-dark: #0891b2;
            --dark-color: #1a2e05;
            --light-color: #f0fdf4;
            /* Minecraft Green Glassmorphism */
            --glass-bg: rgba(34, 197, 94, 0.12);
            --glass-border: rgba(74, 222, 128, 0.25);
            --glass-shadow: 0 8px 32px 0 rgba(22, 163, 74, 0.4);
            --border-radius: 8px;
            --border-radius-lg: 12px;
            --border-radius-xl: 16px;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.1);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.15), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.2), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.25), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.35);
            --shadow-glass: 0 8px 32px 0 rgba(22, 163, 74, 0.4);
            --blur-sm: blur(4px);
            --blur-md: blur(8px);
            --blur-lg: blur(16px);
            --blur-xl: blur(24px);

            /* Minecraft Green Text Colors */
            --text-primary: rgba(255, 255, 255, 0.98);
            --text-secondary: rgba(255, 255, 255, 0.88);
            --text-muted: rgba(255, 255, 255, 0.72);
            --text-muted-light: rgba(255, 255, 255, 0.55);
            --text-accent: #4ade80;
            --text-accent-light: #86efac;
            --text-success: #22c55e;
            --text-warning: #facc15;
            --text-danger: #f87171;
            --text-info: #22d3ee;
            --text-code: #dcfce7;
            --text-code-bg: rgba(20, 83, 45, 0.8);
        }

        * {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            box-sizing: border-box;
        }

        /* Minecraft Background */
        body {
            background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80');
            background-position: center;
            background-size: cover;
            background-attachment: fixed;
            min-height: 100vh;
            position: relative;
            overflow-x: hidden;
            scroll-behavior: auto;
        }

        /* Minecraft green overlay for better contrast */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(20, 83, 45, 0.6);
            pointer-events: none;
            z-index: -1;
        }

        /* Minecraft floating particles overlay */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 20%, rgba(74, 222, 128, 0.12) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(34, 197, 94, 0.10) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(22, 163, 74, 0.08) 0%, transparent 50%);
            animation: subtleFloat 30s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes subtleFloat {
            0%, 100% {
                transform: translateY(0px) scale(1);
                opacity: 0.7;
            }
            50% {
                transform: translateY(-10px) scale(1.05);
                opacity: 1;
            }
        }

        /* Minecraft block pattern overlay */
        .minecraft-pattern::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32"><defs><pattern id="minecraft" width="32" height="32" patternUnits="userSpaceOnUse"><rect width="32" height="32" fill="rgba(74,222,128,0.03)"/><rect x="0" y="0" width="16" height="16" fill="rgba(34,197,94,0.02)"/><rect x="16" y="16" width="16" height="16" fill="rgba(34,197,94,0.02)"/></pattern></defs><rect width="100%" height="100%" fill="url(%23minecraft)"/></svg>');
            pointer-events: none;
            z-index: -1;
        }

        /* Enhanced Glass Morphism */
        .glass {
            background: var(--glass-bg);
            backdrop-filter: var(--blur-lg);
            -webkit-backdrop-filter: var(--blur-lg);
            border: 1px solid var(--glass-border);
            box-shadow: var(--shadow-glass);
            border-radius: var(--border-radius);
        }

        .glass-intense {
            background: rgba(255, 255, 255, 0.12);
            backdrop-filter: var(--blur-xl);
            -webkit-backdrop-filter: var(--blur-xl);
            border: 1px solid rgba(255, 255, 255, 0.25);
            box-shadow: var(--shadow-2xl);
            border-radius: var(--border-radius-lg);
        }

        /* Minecraft Green Glassmorphism Navbar */
        .navbar {
            backdrop-filter: blur(16px) saturate(180%) !important;
            -webkit-backdrop-filter: blur(16px) saturate(180%) !important;
            background-color: rgba(20, 83, 45, 0.8) !important;
            border-bottom: 1px solid rgba(74, 222, 128, 0.2);
            box-shadow: 0 8px 32px rgba(22, 163, 74, 0.4);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            right: 0 !important;
            z-index: 1030 !important;
            overflow: hidden;
            padding: 1rem 0;
        }

        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(74, 222, 128, 0.15), transparent);
            transition: left 0.8s ease;
        }

        .navbar:hover::before {
            left: 100%;
        }

        .navbar-brand {
            font-weight: 700;
            color: #ffffff !important;
            font-size: 1.6rem;
            text-shadow: 0 2px 8px rgba(74, 222, 128, 0.5);
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
        }

        .navbar-brand i {
            color: #4ade80;
            margin-right: 0.5rem;
            font-size: 1.8rem;
            filter: drop-shadow(0 0 8px rgba(74, 222, 128, 0.6));
        }

        .navbar-brand:hover {
            transform: scale(1.05);
            text-shadow: 0 4px 16px rgba(74, 222, 128, 0.8);
            color: #4ade80 !important;
        }

        .nav-link {
            color: rgba(255, 255, 255, 0.9) !important;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 12px;
            margin: 0 6px;
            padding: 12px 20px !important;
            position: relative;
            overflow: hidden;
            border: 1px solid transparent;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(74, 222, 128, 0.15), rgba(34, 197, 94, 0.1));
            opacity: 0;
            transition: opacity 0.3s ease;
            border-radius: 12px;
        }

        .nav-link:hover::before {
            opacity: 1;
        }

        .nav-link:hover {
            color: #ffffff !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(74, 222, 128, 0.3);
            border-color: rgba(74, 222, 128, 0.4);
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(74, 222, 128, 0.2), rgba(34, 197, 94, 0.2));
            color: #ffffff !important;
            border-color: rgba(74, 222, 128, 0.5);
            box-shadow: 0 4px 16px rgba(74, 222, 128, 0.3);
        }

        .container {
            position: relative;
            z-index: 1;
        }

        /* Responsive adjustments for main content positioning */
        @media (max-width: 768px) {
            .container.fade-in {
                margin-top: 65px !important;
                padding-top: 1rem !important;
            }
        }

        /* Minecraft Green Glassmorphism Cards */
        .card {
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.75);
            border: 1px solid rgba(74, 222, 128, 0.2);
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(22, 163, 74, 0.3);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
            position: relative;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(74, 222, 128, 0.6), transparent);
        }

        .card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 25px 50px rgba(34, 197, 94, 0.3);
            border-color: rgba(74, 222, 128, 0.3);
            background-color: rgba(20, 83, 45, 0.85);
        }

        .card-header {
            background: linear-gradient(135deg, rgba(74, 222, 128, 0.15) 0%, rgba(34, 197, 94, 0.1) 100%);
            border-bottom: 1px solid rgba(74, 222, 128, 0.3);
            padding: 1.5rem;
            position: relative;
            overflow: hidden;
        }

        .card-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(74, 222, 128, 0.2), transparent);
            transition: left 0.8s ease;
        }

        .card:hover .card-header::before {
            left: 100%;
        }

        .card-title {
            color: #ffffff;
            font-weight: 600;
            margin: 0;
            text-shadow: 0 2px 8px rgba(74, 222, 128, 0.4);
        }

        /* Enhanced Status Indicators */
        .status-online {
            color: var(--success-light);
            animation: pulseGlow 2s infinite;
            filter: drop-shadow(0 0 8px rgba(52, 211, 153, 0.6));
        }

        .status-offline {
            color: var(--danger-light);
            animation: pulseGlow 2s infinite;
            filter: drop-shadow(0 0 8px rgba(248, 113, 113, 0.6));
        }

        @keyframes pulseGlow {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.1);
            }
        }

        /* Minecraft Green Player Cards */
        .player-card {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.65);
            border: 1px solid rgba(74, 222, 128, 0.2);
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        .player-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, rgba(74, 222, 128, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .player-card:hover::before {
            opacity: 1;
        }

        .player-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px rgba(34, 197, 94, 0.3);
            border-color: rgba(74, 222, 128, 0.3);
            background-color: rgba(20, 83, 45, 0.8);
        }

        /* Enhanced Glass Buttons */
        .btn {
            border-radius: 12px;
            font-weight: 500;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            position: relative;
            overflow: hidden;
            backdrop-filter: var(--blur-sm);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.6s ease;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.3s ease, height 0.3s ease;
        }

        .btn:active::after {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 8px 25px rgba(74, 222, 128, 0.4);
            border-color: rgba(74, 222, 128, 0.3);
        }

        .btn-primary:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(74, 222, 128, 0.5);
            background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, var(--success-dark) 100%);
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
            border-color: rgba(16, 185, 129, 0.3);
        }

        .btn-success:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(16, 185, 129, 0.5);
            background: linear-gradient(135deg, var(--success-light) 0%, var(--success-color) 100%);
        }

        .btn-warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, var(--warning-dark) 100%);
            box-shadow: 0 8px 25px rgba(245, 158, 11, 0.4);
            border-color: rgba(245, 158, 11, 0.3);
        }

        .btn-warning:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(245, 158, 11, 0.5);
            background: linear-gradient(135deg, var(--warning-light) 0%, var(--warning-color) 100%);
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, var(--danger-dark) 100%);
            box-shadow: 0 8px 25px rgba(239, 68, 68, 0.4);
            border-color: rgba(239, 68, 68, 0.3);
        }

        .btn-danger:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 35px rgba(239, 68, 68, 0.5);
            background: linear-gradient(135deg, var(--danger-light) 0%, var(--danger-color) 100%);
        }

        .btn-outline-primary,
        .btn-outline-success,
        .btn-outline-warning,
        .btn-outline-danger,
        .btn-outline-info,
        .btn-outline-secondary {
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.6);
            border: 1px solid rgba(74, 222, 128, 0.2);
            color: rgba(255, 255, 255, 0.9);
        }

        .btn-outline-primary:hover {
            background: rgba(74, 222, 128, 0.2);
            border-color: var(--primary-color);
            color: white;
            transform: translateY(-2px);
        }

        .btn-outline-success:hover {
            background: rgba(16, 185, 129, 0.2);
            border-color: var(--success-color);
            color: white;
            transform: translateY(-2px);
        }

        .btn-outline-warning:hover {
            background: rgba(245, 158, 11, 0.2);
            border-color: var(--warning-color);
            color: white;
            transform: translateY(-2px);
        }

        .btn-outline-danger:hover {
            background: rgba(239, 68, 68, 0.2);
            border-color: var(--danger-color);
            color: white;
            transform: translateY(-2px);
        }

        .console-output {
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.8);
            border: 1px solid rgba(74, 222, 128, 0.2);
            color: #dcfce7;
            font-family: 'JetBrains Mono', 'Fira Code', 'Courier New', monospace;
            font-size: 13px;
            height: 400px;
            overflow-y: auto;
            padding: 20px;
            border-radius: 12px;
            position: relative;
        }

        .console-output::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 30px;
            background: linear-gradient(90deg, #4facfe 0%, #8b5cf6 50%, #10b981 100%);
            border-radius: var(--border-radius) var(--border-radius) 0 0;
        }

        .console-output::after {
            content: '● ● ●';
            position: absolute;
            top: 8px;
            left: 15px;
            color: #0f0f23;
            font-size: 12px;
            font-weight: bold;
        }

        .activity-item {
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.7);
            border: 1px solid rgba(74, 222, 128, 0.2);
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 12px;
            border-left: 4px solid var(--info-color);
            transition: all 0.3s ease;
            animation: slideInRight 0.5s ease;
            color: rgba(255, 255, 255, 0.9);
        }

        .activity-item:hover {
            transform: translateX(4px);
            box-shadow: 0 8px 25px rgba(34, 197, 94, 0.2);
        }

        .activity-join {
            border-left-color: var(--success-color);
            background: rgba(16, 185, 129, 0.15);
        }

        .activity-leave {
            border-left-color: var(--danger-color);
            background: rgba(239, 68, 68, 0.15);
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Enhanced Glassmorphism Badges */
        .badge {
            border-radius: 12px;
            font-weight: 500;
            padding: 8px 14px;
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.7);
            border: 1px solid rgba(74, 222, 128, 0.2);
            color: rgba(255, 255, 255, 0.95);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }

        .badge:hover {
            transform: scale(1.05);
            background-color: rgba(20, 83, 45, 0.9);
            border-color: rgba(74, 222, 128, 0.3);
        }

        .bg-success {
            background: linear-gradient(135deg, var(--success-color), var(--success-light)) !important;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }

        .bg-danger {
            background: linear-gradient(135deg, var(--danger-color), var(--danger-light)) !important;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
        }

        .bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-light)) !important;
            box-shadow: 0 4px 15px rgba(99, 102, 241, 0.3);
        }

        /* Minecraft Green Form Controls */
        .form-control, .form-select {
            border-radius: 8px;
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.6);
            border: 1px solid rgba(74, 222, 128, 0.2);
            color: rgba(255, 255, 255, 0.95);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }

        .form-control:focus, .form-select:focus {
            border-color: rgba(74, 222, 128, 0.4);
            box-shadow: 0 0 0 3px rgba(74, 222, 128, 0.2);
            background-color: rgba(20, 83, 45, 0.8);
            transform: scale(1.02);
        }

        /* Minecraft Green Modals */
        .modal-content {
            border-radius: 12px;
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            background-color: rgba(20, 83, 45, 0.9);
            border: 1px solid rgba(74, 222, 128, 0.2);
            box-shadow: 0 25px 50px rgba(22, 163, 74, 0.4);
            position: relative;
            overflow: hidden;
        }

        .modal-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(74, 222, 128, 0.6), transparent);
        }

        .modal-header {
            border-bottom: 1px solid rgba(74, 222, 128, 0.3);
            background: rgba(74, 222, 128, 0.1);
            color: #ffffff;
        }

        .modal-footer {
            border-top: 1px solid rgba(74, 222, 128, 0.3);
            background: rgba(74, 222, 128, 0.05);
        }

        .modal-title {
            color: #ffffff;
            text-shadow: 0 2px 8px rgba(74, 222, 128, 0.4);
        }

        .modal-body {
            color: rgba(255, 255, 255, 0.95);
        }

        /* Modal Animation */
        .modal.fade .modal-dialog {
            transform: scale(0.8) translateY(-50px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .modal.show .modal-dialog {
            transform: scale(1) translateY(0);
        }

        /* Loading animations */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Fade in animation for page content */
        .fade-in {
            animation: fadeIn 0.6s ease;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Minecraft Green Scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(20, 83, 45, 0.3);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(74, 222, 128, 0.4);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(74, 222, 128, 0.6);
        }

        /* Enhanced Text Color Utilities for Glassmorphism */
        .text-primary-glass {
            color: var(--text-primary) !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .text-secondary-glass {
            color: var(--text-secondary) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .text-muted-glass {
            color: var(--text-muted) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .text-muted-light-glass {
            color: var(--text-muted-light) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .text-accent-glass {
            color: var(--text-accent) !important;
            text-shadow: 0 1px 3px rgba(74, 222, 128, 0.3);
            filter: drop-shadow(0 0 4px rgba(74, 222, 128, 0.4));
        }

        .text-accent-light-glass {
            color: var(--text-accent-light) !important;
            text-shadow: 0 1px 3px rgba(134, 239, 172, 0.3);
            filter: drop-shadow(0 0 4px rgba(134, 239, 172, 0.4));
        }

        /* Override Bootstrap text utilities for better glassmorphism compatibility */
        .text-white {
            color: var(--text-primary) !important;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .text-white-50 {
            color: var(--text-muted) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .text-muted {
            color: var(--text-muted) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .text-light {
            color: var(--text-secondary) !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        /* Enhanced color-specific text utilities */
        .text-success {
            color: var(--text-success) !important;
            text-shadow: 0 1px 3px rgba(52, 211, 153, 0.3);
            filter: drop-shadow(0 0 4px rgba(52, 211, 153, 0.3));
        }

        .text-warning {
            color: var(--text-warning) !important;
            text-shadow: 0 1px 3px rgba(251, 191, 36, 0.3);
            filter: drop-shadow(0 0 4px rgba(251, 191, 36, 0.3));
        }

        .text-danger {
            color: var(--text-danger) !important;
            text-shadow: 0 1px 3px rgba(248, 113, 113, 0.3);
            filter: drop-shadow(0 0 4px rgba(248, 113, 113, 0.3));
        }

        .text-info {
            color: var(--text-info) !important;
            text-shadow: 0 1px 3px rgba(96, 165, 250, 0.3);
            filter: drop-shadow(0 0 4px rgba(96, 165, 250, 0.3));
        }

        .text-primary {
            color: var(--primary-light) !important;
            text-shadow: 0 1px 3px rgba(129, 140, 248, 0.3);
            filter: drop-shadow(0 0 4px rgba(129, 140, 248, 0.3));
        }

        .text-secondary {
            color: var(--secondary-light) !important;
            text-shadow: 0 1px 3px rgba(167, 139, 250, 0.3);
            filter: drop-shadow(0 0 4px rgba(167, 139, 250, 0.3));
        }

        /* Enhanced code text styling */
        code {
            color: var(--text-code) !important;
            background-color: var(--text-code-bg) !important;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 2px 6px;
            font-size: 0.875em;
            text-shadow: none;
            backdrop-filter: blur(8px);
        }

        /* Enhanced heading colors */
        h1, h2, h3, h4, h5, h6 {
            color: var(--text-primary);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        /* Enhanced paragraph and body text */
        p, .card-body, .modal-body {
            color: var(--text-secondary);
        }

        /* Enhanced small text and captions */
        small, .small, .form-text {
            color: var(--text-muted) !important;
        }

        /* Enhanced link colors */
        a {
            color: var(--text-accent);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        a:hover {
            color: var(--text-accent-light);
            text-shadow: 0 1px 3px rgba(74, 222, 128, 0.4);
        }

        /* Enhanced form label colors */
        .form-label {
            color: var(--text-secondary) !important;
            font-weight: 500;
        }

        /* Enhanced alert text colors */
        .alert {
            color: var(--text-primary) !important;
        }

        .alert-heading {
            color: var(--text-primary) !important;
        }

        /* Enhanced badge text contrast */
        .badge {
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
        }

        /* Enhanced table text colors */
        .table {
            color: var(--text-secondary);
        }

        .table th {
            color: var(--text-primary);
        }

        /* Enhanced list text colors */
        .list-group-item {
            color: var(--text-secondary);
            background-color: rgba(17, 25, 40, 0.6);
            border-color: rgba(255, 255, 255, 0.125);
        }

        /* Enhanced dropdown text colors */
        .dropdown-menu {
            background-color: rgba(20, 83, 45, 0.9);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(74, 222, 128, 0.2);
        }

        .dropdown-item {
            color: var(--text-secondary);
        }

        .dropdown-item:hover {
            color: var(--text-primary);
            background-color: rgba(74, 222, 128, 0.2);
        }
    </style>
</head>
<body class="minecraft-pattern">
    <!-- Floating Particles -->
    <div class="particles" id="particles"></div>

    <nav class="navbar navbar-expand-lg fixed-top">
        <div class="container">
            <a class="navbar-brand animate__animated animate__fadeInLeft" href="{{ url_for('index') }}">
                <i class="fas fa-cube me-2"></i>Minecraft Server Manager
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'index' else '' }}" href="{{ url_for('index') }}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'players' else '' }}" href="{{ url_for('players') }}">
                            <i class="fas fa-users me-1"></i>Players
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ 'active' if request.endpoint == 'console' else '' }}" href="{{ url_for('console') }}">
                            <i class="fas fa-terminal me-1"></i>Console
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="http://localhost:25580" target="_blank">
                            <i class="fas fa-folder me-1"></i>Files
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Toast Container -->
    <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999; margin-top: 70px;">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="toast show animate__animated animate__slideInRight" role="alert" data-bs-autohide="true" data-bs-delay="5000">
                        <div class="toast-header">
                            <i class="fas fa-{{ 'check-circle text-success' if category == 'success' else 'exclamation-triangle text-warning' if category == 'warning' else 'times-circle text-danger' }} me-2"></i>
                            <strong class="me-auto">{{ 'Success' if category == 'success' else 'Warning' if category == 'warning' else 'Error' }}</strong>
                            <small class="text-muted">just now</small>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">
                            {{ message }}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <div class="container fade-in" style="margin-top: 70px; padding-top: 1rem;">
        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Modern Toast System
        class ToastManager {
            static show(message, type = 'success', duration = 5000) {
                const toastContainer = document.querySelector('.toast-container');
                const toastId = 'toast-' + Date.now();

                const iconMap = {
                    success: 'check-circle text-success',
                    error: 'times-circle text-danger',
                    warning: 'exclamation-triangle text-warning',
                    info: 'info-circle text-info'
                };

                const titleMap = {
                    success: 'Success',
                    error: 'Error',
                    warning: 'Warning',
                    info: 'Information'
                };

                const toastHTML = `
                    <div id="${toastId}" class="toast animate__animated animate__slideInRight" role="alert" data-bs-autohide="true" data-bs-delay="${duration}">
                        <div class="toast-header">
                            <i class="fas fa-${iconMap[type]} me-2"></i>
                            <strong class="me-auto">${titleMap[type]}</strong>
                            <small class="text-muted">just now</small>
                            <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                        </div>
                        <div class="toast-body">
                            ${message}
                        </div>
                    </div>
                `;

                toastContainer.insertAdjacentHTML('beforeend', toastHTML);
                const toastElement = document.getElementById(toastId);
                const toast = new bootstrap.Toast(toastElement);
                toast.show();

                // Remove from DOM after hiding
                toastElement.addEventListener('hidden.bs.toast', () => {
                    toastElement.remove();
                });
            }
        }

        // Loading state management
        class LoadingManager {
            static show(element) {
                element.classList.add('loading');
                element.disabled = true;
            }

            static hide(element) {
                element.classList.remove('loading');
                element.disabled = false;
            }
        }

        // Enhanced auto-refresh functionality
        let refreshInterval;
        let isRefreshing = false;

        async function refreshData() {
            if (isRefreshing) return;
            isRefreshing = true;

            try {
                if (window.location.pathname === '/') {
                    const response = await fetch('/api/status');
                    const data = await response.json();

                    if (data.players) {
                        const onlineCountEl = document.getElementById('online-count');
                        const maxPlayersEl = document.getElementById('max-players');

                        if (onlineCountEl && maxPlayersEl) {
                            // Animate number changes
                            animateNumber(onlineCountEl, parseInt(onlineCountEl.textContent), data.players.online_count);
                            animateNumber(maxPlayersEl, parseInt(maxPlayersEl.textContent), data.players.max_players);
                        }

                        // Update last refresh indicator
                        updateLastRefresh();
                    }
                } else if (window.location.pathname === '/players') {
                    // Refresh player data with detailed information
                    const response = await fetch('/api/players/detailed');
                    const data = await response.json();
                    // Update player count badge
                    const badge = document.querySelector('.badge');
                    if (badge && data.online_count !== undefined) {
                        badge.textContent = `${data.online_count}/${data.max_players}`;
                    }
                    // Update player list if updatePlayerList function exists
                    if (typeof updatePlayerList === 'function' && data.detailed_players) {
                        updatePlayerList(data.detailed_players);
                    }
                }
            } catch (error) {
                console.log('Refresh error:', error);
            } finally {
                isRefreshing = false;
            }
        }

        function animateNumber(element, from, to) {
            if (from === to) return;

            const duration = 500;
            const steps = 20;
            const stepValue = (to - from) / steps;
            let current = from;
            let step = 0;

            const timer = setInterval(() => {
                step++;
                current += stepValue;

                if (step >= steps) {
                    element.textContent = to;
                    clearInterval(timer);
                } else {
                    element.textContent = Math.round(current);
                }
            }, duration / steps);
        }

        function updateLastRefresh() {
            const refreshElements = document.querySelectorAll('.last-refresh');
            const now = new Date().toLocaleTimeString();
            refreshElements.forEach(el => el.textContent = now);
        }

        // Initialize auto-refresh
        function startAutoRefresh(interval = 30000) {
            if (refreshInterval) clearInterval(refreshInterval);
            refreshInterval = setInterval(refreshData, interval);
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
                refreshInterval = null;
            }
        }



        // Floating Particles System
        class ParticleSystem {
            constructor() {
                this.particles = [];
                this.container = document.getElementById('particles');
                this.init();
            }

            init() {
                this.createParticles();
                this.animate();
            }

            createParticles() {
                for (let i = 0; i < 50; i++) {
                    this.createParticle();
                }
            }

            createParticle() {
                const particle = document.createElement('div');
                particle.className = 'particle';

                const size = Math.random() * 4 + 2;
                const startX = Math.random() * window.innerWidth;
                const duration = Math.random() * 10 + 15;
                const delay = Math.random() * 5;

                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = startX + 'px';
                particle.style.animationDuration = duration + 's';
                particle.style.animationDelay = delay + 's';

                this.container.appendChild(particle);
                this.particles.push(particle);

                // Remove particle after animation
                setTimeout(() => {
                    if (particle.parentNode) {
                        particle.parentNode.removeChild(particle);
                        this.particles = this.particles.filter(p => p !== particle);
                    }
                }, (duration + delay) * 1000);
            }

            animate() {
                // Continuously create new particles
                setInterval(() => {
                    if (this.particles.length < 50) {
                        this.createParticle();
                    }
                }, 300);
            }
        }

        // Enhanced Loading Animation
        class EnhancedLoadingManager {
            static show(element, text = 'Loading...') {
                element.classList.add('loading');
                element.disabled = true;
                const originalText = element.innerHTML;
                element.setAttribute('data-original-text', originalText);
                element.innerHTML = `<i class="fas fa-spinner fa-spin me-2"></i>${text}`;
            }

            static hide(element) {
                element.classList.remove('loading');
                element.disabled = false;
                const originalText = element.getAttribute('data-original-text');
                if (originalText) {
                    element.innerHTML = originalText;
                    element.removeAttribute('data-original-text');
                }
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            // Ensure page starts at top
            window.scrollTo(0, 0);
            document.documentElement.scrollTop = 0;
            document.body.scrollTop = 0;

            // Initialize particle system
            new ParticleSystem();
            // Check for pending toast messages
            const pendingToast = sessionStorage.getItem('pendingToast');
            if (pendingToast) {
                try {
                    const toastData = JSON.parse(pendingToast);
                    ToastManager.show(toastData.message, toastData.type);
                    sessionStorage.removeItem('pendingToast');
                } catch (e) {
                    console.error('Error parsing pending toast:', e);
                    sessionStorage.removeItem('pendingToast');
                }
            }

            // Initialize tooltips
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Initialize toasts
            const toastElList = [].slice.call(document.querySelectorAll('.toast'));
            toastElList.map(function (toastEl) {
                return new bootstrap.Toast(toastEl);
            });

            // Start auto-refresh
            startAutoRefresh();

            // Add smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add loading states to buttons and handle success messages
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        LoadingManager.show(submitBtn);
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                        submitBtn.disabled = true;
                    }

                    // Store success message for after page reload
                    const successMessage = this.getAttribute('data-success-message');
                    if (successMessage) {
                        sessionStorage.setItem('pendingToast', JSON.stringify({
                            message: successMessage,
                            type: 'success'
                        }));
                    }
                });
            });
        });

        // Ensure page starts at top on load
        window.addEventListener('load', function() {
            window.scrollTo(0, 0);
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', function() {
            stopAutoRefresh();
        });

        // Global error handler
        window.addEventListener('error', function(e) {
            ToastManager.show('An unexpected error occurred. Please try again.', 'error');
        });
    </script>
    {% block scripts %}{% endblock %}
</body>
</html>
